package com.investment.api.model;

import com.investment.model.OHLCV;

import java.time.LocalDate;

/**
 * Response model for OHLCV data.
 */
public class OHLCVResponse {
    private final String symbol;
    private final LocalDate date;
    private final double open;
    private final double high;
    private final double low;
    private final double close;
    private final long volume;
    private final Double bbMiddleBand;
    private final Double bbStdDev;
    private final Double bbUpperBand;
    private final Double bbLowerBand;
    
    public OHLCVResponse(OHLCV ohlcv) {
        this.symbol = ohlcv.getSymbol();
        this.date = ohlcv.getDate();
        this.open = ohlcv.getOpen();
        this.high = ohlcv.getHigh();
        this.low = ohlcv.getLow();
        this.close = ohlcv.getClose();
        this.volume = ohlcv.getVolume();
        this.bbMiddleBand = ohlcv.getBbMiddleBand();
        this.bbStdDev = ohlcv.getBbStdDev();
        this.bbUpperBand = ohlcv.getBbUpperBand();
        this.bbLowerBand = ohlcv.getBbLowerBand();
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public LocalDate getDate() {
        return date;
    }
    
    public double getOpen() {
        return open;
    }
    
    public double getHigh() {
        return high;
    }
    
    public double getLow() {
        return low;
    }
    
    public double getClose() {
        return close;
    }
    
    public long getVolume() {
        return volume;
    }

    public Double getBbMiddleBand() {
        return bbMiddleBand;
    }

    public Double getBbStdDev() {
        return bbStdDev;
    }

    public Double getBbUpperBand() {
        return bbUpperBand;
    }

    public Double getBbLowerBand() {
        return bbLowerBand;
    }
}
