package com.investment.database

import spock.lang.Specification

import java.sql.Connection
import java.sql.DriverManager
import java.sql.Statement

/**
 * Spock specification for testing database migration from old to new schema.
 */
class DatabaseMigrationSpec extends Specification {

    String testDbUrl = "****************************************"

    def setup() {
        // Clean up any existing test database
        new File("./data/migration_test.duckdb").delete()
    }

    def cleanup() {
        // Clean up test database file
        new File("./data/migration_test.duckdb").delete()
    }

    def "should migrate from old schema to new schema"() {
        given: "a database with old schema"
        createOldSchema()
        insertOldSchemaData()

        when: "initializing DatabaseManager (which triggers migration)"
        DatabaseManager.setDbUrl(testDbUrl)
        DatabaseManager dbManager = new DatabaseManager()
        dbManager.initDatabase()

        then: "the schema should be migrated to new format"
        def columns = getTableColumns("instruments")
        columns.contains("symbol")
        columns.contains("name")
        columns.contains("instrument_type") // renamed from 'type'
        columns.contains("market_cap")
        columns.contains("country")
        columns.contains("ipo_year")
        columns.contains("sector")
        columns.contains("industry")
        columns.contains("created_at")
        columns.contains("updated_at")

        and: "existing data should be preserved"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.symbol == "AAPL"
        instrument.name == "Apple Inc."
        instrument.instrument_type == "US_STOCK"

        cleanup:
        dbManager?.closeConnection()
    }

    def "should migrate from schema version 2 to version 3 adding Bollinger Band columns"() {
        given: "a database with schema version 2"
        createSchemaVersion2()
        insertSchemaVersion2Data()

        when: "initializing DatabaseManager (which triggers migration to version 3)"
        DatabaseManager.setDbUrl(testDbUrl)
        DatabaseManager dbManager = new DatabaseManager()
        dbManager.initDatabase()

        then: "the OHLCV table should have Bollinger Band columns"
        def ohlcvColumns = getTableColumns("ohlcv")
        ohlcvColumns.contains("bb_middle_band")
        ohlcvColumns.contains("bb_std_dev")
        ohlcvColumns.contains("bb_upper_band")
        ohlcvColumns.contains("bb_lower_band")

        and: "existing OHLCV data should be preserved with null Bollinger Band values"
        def ohlcvData = getOhlcvFromDb("AAPL", "2024-01-01")
        ohlcvData != null
        ohlcvData.symbol == "AAPL"
        ohlcvData.open == 150.0
        ohlcvData.bb_middle_band == null
        ohlcvData.bb_std_dev == null
        ohlcvData.bb_upper_band == null
        ohlcvData.bb_lower_band == null

        cleanup:
        dbManager?.closeConnection()
    }

    private void createOldSchema() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            
            // Create schema version table
            stmt.execute(
                "CREATE TABLE schema_version (" +
                "version INTEGER PRIMARY KEY, " +
                "applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")"
            )
            
            // Create old instruments table schema
            stmt.execute(
                "CREATE TABLE instruments (" +
                "symbol VARCHAR(20) PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "type VARCHAR(20) NOT NULL, " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP" +
                ")"
            )
            
            // Insert version 1 to indicate old schema
            stmt.execute("INSERT INTO schema_version (version) VALUES (1)")
        } finally {
            conn.close()
        }
    }

    private void insertOldSchemaData() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            stmt.execute(
                "INSERT INTO instruments (symbol, name, type, created_at, updated_at) " +
                "VALUES ('AAPL', 'Apple Inc.', 'US_STOCK', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)"
            )
        } finally {
            conn.close()
        }
    }

    private List<String> getTableColumns(String tableName) {
        List<String> columns = []
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM ${tableName} LIMIT 0")
            def metaData = rs.getMetaData()
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                columns.add(metaData.getColumnName(i))
            }
        } finally {
            conn.close()
        }
        return columns
    }

    private Map getInstrumentFromDb(String symbol) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM instruments WHERE symbol = '${symbol}'")
            if (rs.next()) {
                return [
                    symbol: rs.getString("symbol"),
                    name: rs.getString("name"),
                    instrument_type: rs.getString("instrument_type")
                ]
            }
        } finally {
            conn.close()
        }
        return null
    }

    private void createSchemaVersion2() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()

            // Create schema version table
            stmt.execute(
                "CREATE TABLE schema_version (" +
                "version INTEGER PRIMARY KEY, " +
                "applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")"
            )

            // Create instruments table with version 2 schema
            stmt.execute(
                "CREATE TABLE instruments (" +
                "symbol VARCHAR(20) PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "instrument_type VARCHAR(20) NOT NULL, " +
                "market_cap DECIMAL(20,2), " +
                "country VARCHAR(50), " +
                "ipo_year INTEGER, " +
                "sector VARCHAR(100), " +
                "industry VARCHAR(100), " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP" +
                ")"
            )

            // Create OHLCV table without Bollinger Band columns (version 2)
            stmt.execute(
                "CREATE TABLE ohlcv (" +
                "symbol VARCHAR(20) NOT NULL, " +
                "date DATE NOT NULL, " +
                "open DECIMAL(20,6) NOT NULL, " +
                "high DECIMAL(20,6) NOT NULL, " +
                "low DECIMAL(20,6) NOT NULL, " +
                "close DECIMAL(20,6) NOT NULL, " +
                "volume BIGINT NOT NULL, " +
                "PRIMARY KEY (symbol, date), " +
                "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                ")"
            )

            // Insert version 2 to indicate current schema
            stmt.execute("INSERT INTO schema_version (version) VALUES (2)")
        } finally {
            conn.close()
        }
    }

    private void insertSchemaVersion2Data() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()

            // Insert instrument
            stmt.execute(
                "INSERT INTO instruments (symbol, name, instrument_type, created_at, updated_at) " +
                "VALUES ('AAPL', 'Apple Inc.', 'US_STOCK', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)"
            )

            // Insert OHLCV data
            stmt.execute(
                "INSERT INTO ohlcv (symbol, date, open, high, low, close, volume) " +
                "VALUES ('AAPL', '2024-01-01', 150.0, 155.0, 148.0, 152.0, 1000000)"
            )
        } finally {
            conn.close()
        }
    }

    private Map getOhlcvFromDb(String symbol, String date) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM ohlcv WHERE symbol = '${symbol}' AND date = '${date}'")
            if (rs.next()) {
                return [
                    symbol: rs.getString("symbol"),
                    date: rs.getDate("date"),
                    open: rs.getDouble("open"),
                    high: rs.getDouble("high"),
                    low: rs.getDouble("low"),
                    close: rs.getDouble("close"),
                    volume: rs.getLong("volume"),
                    bb_middle_band: rs.getObject("bb_middle_band", Double.class),
                    bb_std_dev: rs.getObject("bb_std_dev", Double.class),
                    bb_upper_band: rs.getObject("bb_upper_band", Double.class),
                    bb_lower_band: rs.getObject("bb_lower_band", Double.class)
                ]
            }
        } finally {
            conn.close()
        }
        return null
    }
}
