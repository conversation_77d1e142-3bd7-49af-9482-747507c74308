package com.investment;

import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.io.File;

/**
 * Simple test to verify Bollinger Band functionality works.
 */
public class BollingerBandTest {

    private DatabaseManager dbManager;
    
    @BeforeEach
    void setUp() {
        // Clean up any existing test database
        new File("./data/bb_test.duckdb").delete();

        // Set up test database
        DatabaseManager.setDbUrl("*********************************");
        dbManager = new DatabaseManager();
        dbManager.initDatabase();
    }

    @AfterEach
    void tearDown() {
        if (dbManager != null) {
            dbManager.closeConnection();
        }
        // Clean up test database file
        new File("./data/bb_test.duckdb").delete();
    }

    @Test
    void testBollingerBandFunctionality() {
        try {
            
            // Create test instrument
            dbManager.saveInstrument("TEST", "Test Stock", "US_STOCK");
            
            // Create OHLCV data with Bollinger Bands
            List<OHLCV> testData = Arrays.asList(
                new OHLCV("TEST", LocalDate.of(2024, 1, 1), 100.0, 105.0, 98.0, 102.0, 1000000L,
                         101.5, 2.5, 106.5, 96.5), // with Bollinger Bands
                new OHLCV("TEST", LocalDate.of(2024, 1, 2), 102.0, 107.0, 100.0, 104.0, 1100000L) // without Bollinger Bands
            );
            
            // Save the data
            dbManager.saveOHLCVData(testData);
            System.out.println("✓ Successfully saved OHLCV data with Bollinger Bands");
            
            // Retrieve the data
            List<OHLCV> retrievedData = dbManager.getOHLCVData("TEST", 
                LocalDate.of(2024, 1, 1), LocalDate.of(2024, 1, 2));
            
            System.out.println("✓ Successfully retrieved " + retrievedData.size() + " records");
            
            // Verify the data
            for (OHLCV record : retrievedData) {
                System.out.println("Record: " + record.getDate() + 
                    " - Close: " + record.getClose() + 
                    " - BB Middle: " + record.getBbMiddleBand() + 
                    " - BB Upper: " + record.getBbUpperBand() + 
                    " - BB Lower: " + record.getBbLowerBand());
            }
            
            // Verify the data
            assertEquals(2, retrievedData.size(), "Should retrieve 2 records");

            // Verify first record has Bollinger Band data
            OHLCV firstRecord = retrievedData.get(0);
            assertNotNull(firstRecord.getBbMiddleBand(), "First record should have Bollinger Band data");
            assertEquals(101.5, firstRecord.getBbMiddleBand(), 0.001, "BB Middle Band should be 101.5");
            assertEquals(2.5, firstRecord.getBbStdDev(), 0.001, "BB Std Dev should be 2.5");
            assertEquals(106.5, firstRecord.getBbUpperBand(), 0.001, "BB Upper Band should be 106.5");
            assertEquals(96.5, firstRecord.getBbLowerBand(), 0.001, "BB Lower Band should be 96.5");

            // Verify second record has null Bollinger Band data
            OHLCV secondRecord = retrievedData.get(1);
            assertNull(secondRecord.getBbMiddleBand(), "Second record should have null BB Middle Band");
            assertNull(secondRecord.getBbStdDev(), "Second record should have null BB Std Dev");
            assertNull(secondRecord.getBbUpperBand(), "Second record should have null BB Upper Band");
            assertNull(secondRecord.getBbLowerBand(), "Second record should have null BB Lower Band");

        } catch (Exception e) {
            fail("Test failed with exception: " + e.getMessage());
        }
    }
}
